#pragma once
#include <fstream>
#include <fcntl.h>
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/sysmacros.h>
#include "../../include/VecTool.h"
#include "RT.hpp"
using namespace std;
typedef unsigned short UTF16;
#if defined(__aarch64__) && defined(__ANDROID__)
#include "Log.h"
#endif

struct Transform
{
    VecTor3 Scale3D;
    VecTor4 Rotation;
    VecTor3 Translation;
};

struct FMatrix
{
    float M[4][4];
};

class ImGuiTOOL
{
public:
    pid_t Pid = -1;
    int FileDescriPtion = -1;
    float Matrix[4][4] = {0};
    UTF16 BUFFER16[16] = {0};
    Transform MeshTrans, MeshTran;
    uintptr_t ModulesBase[10] = {0};

    TOUCH_INFORMATION touch_information;
    FMatrix XMatrix, BoneMatrix, OutcMatrix;
    uintptr_t MeshAddress = 0, BoneAddress = 0;
    RESOLUTION_INFORMATION resolution_information;
    IMGUISWITCH_INFORMATION imguiswitch_information;

    int readcount(int *c, int num)
    {
        ++*c;
        return num;
    }
    void initialize()
    {
        driver->initialize(Pid);
    }
    bool read(uintptr_t address, void *buffer, size_t size)
    {
        initialize();
        return driver->read(address, buffer, size);
    }
    template <typename start>
    start read(uintptr_t address)
    {
        start buffer;
        if (read(address, &buffer, sizeof(start)))
        {
            return buffer;
        }
        return {};
    }
    template <typename start>
    bool read(uintptr_t address, start *buffer)
    {
        return read(address, buffer, sizeof(start));
    }
    template <typename... s>
    uintptr_t GetPointer(uintptr_t address, s... args)
    {
        int count = 0;
        uintptr_t last_address = 0;
        int array[] = {(readcount(&count, args))...};
        read(address + array[0], &last_address);
        for (int i = 1; i < count; i++)
        {
            if (i == count - 1)
            {
                last_address += array[i];
                return last_address;
            }
            read(last_address + array[i], &last_address);
        }
        return last_address;
    }
    void GetPid(const char *name)
    {
        char buffer[0x100] = "pidof ";
        strcat(buffer, name);
        FILE *file = popen(buffer, "r");
        if (file)
        {
            fscanf(file, "%d", &Pid);
            initialize();
        }
        pclose(file);
    }
    uintptr_t GetModuleAddressTwo(char *name)
    {
        return driver->get_module_base(name);
    }
    ImGuiTOOL()
    {
        memset(&touch_information, 0, sizeof(TOUCH_INFORMATION));
        memset(&resolution_information, 0, sizeof(RESOLUTION_INFORMATION));
        memset(&imguiswitch_information, 0, sizeof(IMGUISWITCH_INFORMATION));
    }

    ~ImGuiTOOL()
    {
        if (FileDescriPtion > 0)
        {
            close(FileDescriPtion);
        }
    }
};
