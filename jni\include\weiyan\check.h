#ifndef CHECK_H
#define CHECK_H

#include "weiyan/Util.h"
#include <fstream>
#include <iostream>
#include <random>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <thread>
#include "safe_exit.h"
std::string ggb;
char buffer[100];
class Check
{
private:
    const string API_DOMAIN = "wy.llua.cn";
    const string KM_PATH = "/sdcard/.Login";
    const string CURRENT_VERSION = "2.8";
    std::thread countdown_thread;
    bool is_running = false;

    void startCountdown(long total_seconds)
    {
        is_running = true;
        countdown_thread = std::thread([this, total_seconds]()
                                       {
            long remaining = total_seconds;
            while (remaining > 0 && is_running) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                remaining--;
            }
            if (remaining <= 0) {
                exit(0);
            } });
        countdown_thread.detach();
    }

    // 从文件读取上次登录的卡密
    string getLastLoginKami()
    {
        string kami;
        ifstream file(KM_PATH);
        if (file.is_open())
        {
            getline(file, kami);
            file.close();
            if (!kami.empty())
            {
                return kami;
            }
        }
        return "";
    }

    // 保存卡密到文件
    void saveKami(const string &kami)
    {
        ofstream file(KM_PATH);
        if (file.is_open())
        {
            file << kami << endl;
            file.close();
        }
        else
        {
            cerr << "无法保存卡密到文件: " << KM_PATH << endl;
        }
    }

public:
    // 检查更新
    void checkUpdate()
    {
        string ini_data = httppost(API_DOMAIN, "v2/4e12a21e5d0e92ecc125c21d3fb5a570", v5e57b57450329e8f1fd7f620fa773927(v5e57b57450329e8f1fd7f620fa773927(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(v5e57b57450329e8f1fd7f620fa773927(k005226acfcd09fa93e76ce0dd230895c("id=4R8R8m8K4j1"), "0C2D7HV4UBhMPkygw8tsaQlEI5+FLd6fN3YnpoOZWcuAevriXjzRT91GbqJSKm/x"), "nfee9883d3fb27fd82dae7d")), "r4689cf7f0e36eeca79ef6b3c")), "f8c26c56337ce5e3b207edf20bae4d91dd1eb")), "le5bade931af6d105ad216a")), "rCIKsLA0xeiacRPpXDQGYB1Tkqd6lu2vgNUFbjnZ3JVSw8o+O54zEWfHh9y/Mmt7"), "plQKrX4kVeLHCgZbPjExqz81f7oan3ASdNyF+vYwt5/imO9W6csJRTu0MG2DBUIh"));

        json ini_json = json::parse(w05b57e39df82976c23e63d36bd1823fb(r081ee716365e37232d9fada923938fe9(ini_data), "s3ba3b8661a4cf04862222eef23f2"));
        if (ini_json.contains("msg"))
        {
            string version = ini_json["msg"]["version"];
            string updateshow = ini_json["msg"]["updateshow"];
            string updateurl = ini_json["msg"]["updateurl"];
            string updatemust = ini_json["msg"]["updatemust"];

            if (version != CURRENT_VERSION)
            {
                safe_exit();
            }
        }
    }

    // 获取公告
    void getNotice()
    {
        string notice_data = httppost(API_DOMAIN, "v2/4e12a21e5d0e92ecc125c21d3fb5a570", v5e57b57450329e8f1fd7f620fa773927(v5e57b57450329e8f1fd7f620fa773927(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(v5e57b57450329e8f1fd7f620fa773927(k005226acfcd09fa93e76ce0dd230895c("id=jR5Z0MrJJ9J"), "0C2D7HV4UBhMPkygw8tsaQlEI5+FLd6fN3YnpoOZWcuAevriXjzRT91GbqJSKm/x"), "nfee9883d3fb27fd82dae7d")), "r4689cf7f0e36eeca79ef6b3c")), "f8c26c56337ce5e3b207edf20bae4d91dd1eb")), "le5bade931af6d105ad216a")), "rCIKsLA0xeiacRPpXDQGYB1Tkqd6lu2vgNUFbjnZ3JVSw8o+O54zEWfHh9y/Mmt7"), "plQKrX4kVeLHCgZbPjExqz81f7oan3ASdNyF+vYwt5/imO9W6csJRTu0MG2DBUIh"));

        json notice_json = json::parse(w05b57e39df82976c23e63d36bd1823fb(r081ee716365e37232d9fada923938fe9(notice_data), "s3ba3b8661a4cf04862222eef23f2"));
        if (notice_json.contains("msg"))
        {
            string gg = notice_json["msg"]["app_gg"];
            if (!gg.empty())
            {
                ggb = gg;
            }
        }
    }

    void fuckshutdown() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
    {
        char path[256];
        pid_t currentPid = getpid();
        ssize_t len = readlink("/proc/self/exe", path, sizeof(path) - 1);
        if (len != -1)
        {
            path[len] = '\0';
            const char *processName = strrchr(path, '/');
            if (processName)
            {
                processName++; // 跳过'/'
                char cmd[512];
                sprintf(cmd, "su -c 'ps -ef | grep %s | grep -v %d | grep -v grep | awk \"{print \\$2}\" | xargs -r kill -9'",
                        processName, currentPid);
                system(cmd);
            }
        }
    }
    // 验证登录
    bool verify()
    {
        string kami = getLastLoginKami();
        if (kami.empty())
        {
            cout << "卡密: ";
            cin >> kami;
        }
        else
        {
            cout << "卡密: " << kami << endl;
        }

        string markcode = getIMEI();
        auto now = chrono::system_clock::now();
        auto timestamp = chrono::duration_cast<chrono::seconds>(now.time_since_epoch()).count();
        string time_str = to_string(timestamp);

        random_device rd;
        mt19937 gen(rd());
        uniform_int_distribution<> dist(100000, 999999);
        string random_str = to_string(dist(gen));

        string sign = tcaa7bbecf195c7ff3578895fb22dba1b("kami=" + kami + "&markcode=" + markcode + "&t=" + time_str + "&q55a1cd0b9fb7ede58b60");

        string verify_data = httppost(API_DOMAIN, "v2/4e12a21e5d0e92ecc125c21d3fb5a570", v5e57b57450329e8f1fd7f620fa773927(v5e57b57450329e8f1fd7f620fa773927(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(l6e6e27ff6bda6dd624b815c229ee24f5(w05b57e39df82976c23e63d36bd1823fb(v5e57b57450329e8f1fd7f620fa773927(k005226acfcd09fa93e76ce0dd230895c("id=77GiG7lkx7Y&kami=" + kami + "&markcode=" + markcode + "&t=" + time_str + "&sign=" + sign + "&value=" + random_str + ""), "0C2D7HV4UBhMPkygw8tsaQlEI5+FLd6fN3YnpoOZWcuAevriXjzRT91GbqJSKm/x"), "nfee9883d3fb27fd82dae7d")), "r4689cf7f0e36eeca79ef6b3c")), "f8c26c56337ce5e3b207edf20bae4d91dd1eb")), "le5bade931af6d105ad216a")), "rCIKsLA0xeiacRPpXDQGYB1Tkqd6lu2vgNUFbjnZ3JVSw8o+O54zEWfHh9y/Mmt7"), "plQKrX4kVeLHCgZbPjExqz81f7oan3ASdNyF+vYwt5/imO9W6csJRTu0MG2DBUIh"));

        json verify_json = json::parse(t734260027af863692d3bbece018ec002(t734260027af863692d3bbece018ec002(ia6dc8b733004c043b9f54b1fe68ab36b(ia6dc8b733004c043b9f54b1fe68ab36b(t734260027af863692d3bbece018ec002(w05b57e39df82976c23e63d36bd1823fb(r081ee716365e37232d9fada923938fe9(verify_data), "h40a72742910e64761e9dcc4983dfa940")), "lqPAIfMXLi9WvDRZuyOSJo5gj73r0CtcTQN1k+Ebp4sw62xBKdnmh/HeGz8FVYaU"), "JXEv2y0CPb6Qa5GZYNewd8/oWKlIHxADSVRFu9pcjkOhMgBm+qL3zTit4fUn17rs"))));

        if (verify_json["ge2672795256bf249fa67bf449d236979"] == 84988 &&
            verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["w07ed159fd66299eed54592c4d95631e4"] == "53f680c4e41d07cf4422bf2448e532b8")
        {

            long server_time = verify_json["n2db8722260a52ad14cd85e6d0c69ab41"];
            if (server_time - timestamp > 30 || server_time - timestamp < -30)
            {
                cout << "设备时间不准" << endl;
                return false;
            }

            string server_time_str = to_string(server_time);
            long code1 = verify_json["ge2672795256bf249fa67bf449d236979"];
            string code1_str = to_string(code1);
            long code2 = verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["lb24d804b833ef02446e377f37d94dc00"];
            string code2_str = to_string(code2);

            // 验证签名
            if (verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["l5737cf2c3104"] != tcaa7bbecf195c7ff3578895fb22dba1b(code2_str + "q55a1cd0b9fb7ede58b60" + time_str + code2_str + sign + "sc3eb6d") ||
                verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["odc5626c3302f37"] != tcaa7bbecf195c7ff3578895fb22dba1b(time_str + random_str + sign + "f2474") ||
                verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["o0ece5ec6cb"] != tcaa7bbecf195c7ff3578895fb22dba1b(server_time_str + code2_str + time_str + time_str + server_time_str + "g40eb") ||
                verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["cd2f1fb7829853e"] != tcaa7bbecf195c7ff3578895fb22dba1b(time_str + code2_str + "q55a1cd0b9fb7ede58b60" + sign + "q55a1cd0b9fb7ede58b60he29ca4"))
            {
                cout << "校验失败" << endl;
                return false;
            }

            // 显示登录信息
            if (verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["gc3817e3b13c2f503552ca36fb83593bc"] == "single")
            {
                cout << "登录成功，剩余可登录次数：" << verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["c773101a2ce23f96723fd461d8e813de3"] << endl;
            }
            else
            {
                long expire_time = verify_json["rb3bec38f7506efaf1c6e5bae0817e279"]["gc684e1276a5d21dc481d0d6b7899a2a5"];
                long current_time = chrono::system_clock::to_time_t(chrono::system_clock::now());
                long time_diff = expire_time - current_time;

                long days = time_diff / (24 * 3600);
                long hours = (time_diff % (24 * 3600)) / 3600;
                long minutes = (time_diff % 3600) / 60;
                long seconds = time_diff % 60;
                sprintf(buffer, "%d天 %d时 %d分", days, hours, minutes, seconds);
                cout << "剩余时间：" << days << "天 " << hours << "时 " << minutes << "分 " << seconds << "秒" << endl;
                // 启动倒计时线程
                startCountdown(time_diff);
            }
            // 保存成功登录的卡密
            saveKami(kami);
            return true;
        }
        else
        {
            string msg = verify_json["rb3bec38f7506efaf1c6e5bae0817e279"];
            cout << msg << endl;
            remove(KM_PATH.c_str());
            return false;
        }
    }
};

#endif // CHECK_H