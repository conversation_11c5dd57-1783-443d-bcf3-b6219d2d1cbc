#ifndef SAFE_EXIT_H
#define SAFE_EXIT_H

#include <signal.h>
#include <unistd.h>
#include <sys/syscall.h>

#define OBFUSCATE_FULL __attribute__((optnone)) __attribute__((noinline)) __attribute__((annotate("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3")))

// 基础退出方式
#define EXIT_NORMAL exit(EXIT_FAILURE)
#define EXIT_DIRECT _exit(1)
#define EXIT_SIGNAL kill(getpid(), SIGKILL)
#define EXIT_SEGV do { int *p = nullptr; *p = 1; } while(0)
#define EXIT_DIV_ZERO do { volatile int zero = 0; volatile int result = 1 / zero; } while(0)
#define EXIT_INT3 __asm__ volatile("int $3")
#define EXIT_SYSCALL syscall(SYS_exit_group, 1)

// 随机组合宏，每次编译可能使用不同的退出顺序
#define EXIT_RANDOM_1 do { EXIT_NORMAL; EXIT_DIRECT; } while(0)
#define EXIT_RANDOM_2 do { EXIT_SIGNAL; EXIT_SEGV; } while(0)
#define EXIT_RANDOM_3 do { EXIT_DIV_ZERO; EXIT_INT3; } while(0)
#define EXIT_RANDOM_4 do { EXIT_SYSCALL; EXIT_SIGNAL; } while(0)

// 主退出宏
#define SAFE_EXIT() do { \
    volatile bool should_exit = true; \
    if(should_exit) { \
        EXIT_RANDOM_1; \
        EXIT_RANDOM_2; \
        EXIT_RANDOM_3; \
        EXIT_RANDOM_4; \
    } \
} while(0)

inline void safe_exit() OBFUSCATE_FULL {
    SAFE_EXIT();
}

#endif // SAFE_EXIT_H 