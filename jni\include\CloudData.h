#ifndef CLOUD_DATA_H
#define CLOUD_DATA_H

#include <iostream>
#include <cstring>
#include <cstdlib>
#include <fstream>
#include <unistd.h>
#include "Curl/curl.h"
#include <dlfcn.h>
#include "txyun.h"

// 云更新相关配置
#define TXYUN_SHARE_URL "https://sharechain.qq.com/eb419c4d20afee7aad96dee22256c5bd"
#define CURRENT_VERSION "3.0"

// 错误码定义
#define ERROR_NETWORK 1
#define ERROR_FILE_ACCESS 2
#define ERROR_VERSION_FORMAT 3
#define ERROR_DOWNLOAD 4
#define ERROR_FILE_PERMISSION 5
#define ERROR_404 6

// 下载配置
#define MAX_RETRY_COUNT 3
#define CONNECT_TIMEOUT 30L
#define DOWNLOAD_TIMEOUT 300L // 5分钟总超时
#define LOW_SPEED_LIMIT 1024L // 1KB/s
#define LOW_SPEED_TIME 60L    // 60秒内低于1KB/s则超时

class CloudData
{
private:
    // 回调函数，用于接收HTTP响应数据
    static size_t WriteCallback(void *contents, size_t size, size_t nmemb, std::string *userp)
    {
        userp->append((char *)contents, size * nmemb);
        return size * nmemb;
    }

    // 进度回调函数
    static int ProgressCallback(void *clientp, double dltotal, double dlnow, double ultotal, double ulnow)
    {
        if (dltotal > 0)
        {
            int percentage = (int)((dlnow / dltotal) * 100);
            std::cout << "\r[+] 下载进度: " << percentage << "% ("
                      << (long)(dlnow / 1024) << "KB/" << (long)(dltotal / 1024) << "KB)" << std::flush;
        }
        return 0;
    }
    // 获取SO库路径
    static std::string GetLibraryPath()
    {
        Dl_info dlInfo;
        if (dladdr((void *)&CloudData::GetLibraryPath, &dlInfo) == 0)
        {
            return "";
        }
        return std::string(dlInfo.dli_fname);
    }

    // 获取目录路径
    static std::string GetDirectoryPath(const std::string &fullPath)
    {
        size_t pos = fullPath.find_last_of("/\\");
        return (pos != std::string::npos) ? fullPath.substr(0, pos) : "";
    }

    // 下载文件（优化大文件下载稳定性）
    static bool DownloadFileWithRetry(CURL *curl, const std::string &url, const std::string &filePath, int maxRetries = MAX_RETRY_COUNT)
    {
        bool success = false;

        for (int retry = 0; retry < maxRetries && !success; retry++)
        {
            if (retry > 0)
            {
                std::cout << "\n[+] 重试下载 (" << retry + 1 << "/" << maxRetries << ")..." << std::endl;
                sleep(2); // 等待2秒后重试
            }

            FILE *fp = fopen(filePath.c_str(), "wb");
            if (!fp)
            {
                std::cerr << "[-] 无法创建更新文件: " << strerror(errno) << std::endl;
                continue;
            }

            // 设置CURL选项 - 优化大文件下载
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, NULL);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, fp);
            curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
            curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, ProgressCallback);

            // 超时设置 - 适应大文件下载
            curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, CONNECT_TIMEOUT);  // 连接超时30秒
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 0L);                      // 禁用总超时，避免大文件下载中断
            curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, LOW_SPEED_LIMIT); // 最低速度1KB/s
            curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, LOW_SPEED_TIME);   // 60秒内低于最低速度则超时

            // 其他稳定性设置
            curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);         // 跟随重定向
            curl_easy_setopt(curl, CURLOPT_MAXREDIRS, 5L);              // 最多5次重定向
            curl_easy_setopt(curl, CURLOPT_USERAGENT, "CloudData/1.0"); // 设置User-Agent

            // 执行下载
            CURLcode res = curl_easy_perform(curl);
            fclose(fp);

            if (res == CURLE_OK)
            {
                long http_code = 0;
                curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);

                if (http_code == 200)
                {
                    success = true;
                   // std::cout << "\n[+] 下载完成" << std::endl;
                }
                else
                {
                    std::cerr << "\n[-] 下载失败，HTTP状态码: " << http_code << std::endl;
                    remove(filePath.c_str());
                }
            }
            else
            {
                // 详细的错误处理
                switch (res)
                {
                case CURLE_OPERATION_TIMEDOUT:
                    std::cerr << "\n[-] 下载超时，正在重试..." << std::endl;
                    break;
                case CURLE_COULDNT_CONNECT:
                    std::cerr << "\n[-] 无法连接到服务器，正在重试..." << std::endl;
                    break;
                case CURLE_PARTIAL_FILE:
                    std::cerr << "\n[-] 文件传输不完整，正在重试..." << std::endl;
                    break;
                case CURLE_RECV_ERROR:
                    std::cerr << "\n[-] 接收数据错误，正在重试..." << std::endl;
                    break;
                case CURLE_SEND_ERROR:
                    std::cerr << "\n[-] 发送数据错误，正在重试..." << std::endl;
                    break;
                default:
                    std::cerr << "\n[-] 下载错误: " << curl_easy_strerror(res) << std::endl;
                    break;
                }
                remove(filePath.c_str());
            }
        }

        return success;
    }

public:
    static bool CheckUpdate()
    {
        // 获取SO库路径
        std::string libraryPath = GetLibraryPath();
        if (libraryPath.empty())
        {
            std::cerr << "[-] 无法获取库路径" << std::endl;
            return false;
        }

        std::string libraryDir = GetDirectoryPath(libraryPath);
        if (libraryDir.empty())
        {
            std::cerr << "[-] 无法获取目录路径" << std::endl;
            return false;
        }

        // 从腾讯云分享链接获取版本号和下载链接
        char title[1024] = {0};  // 版本号
        char brief[1024] = {0};  // 下载直链
        if (!getTxyunContent(TXYUN_SHARE_URL, title, brief))
        {
            std::cerr << "[-] 无法获取更新信息，请检查网络连接" << std::endl;
            exit(ERROR_NETWORK);
        }

        std::string remote_version(title);
        std::string download_url(brief);

        // 清理版本号字符串
        remote_version.erase(remote_version.find_last_not_of("\r\n\t ") + 1);
        download_url.erase(download_url.find_last_not_of("\r\n\t ") + 1);

        if (remote_version.empty() || download_url.empty())
        {
            std::cerr << "[-] 远程版本信息格式错误" << std::endl;
            exit(ERROR_VERSION_FORMAT);
        }
        // 如果远程版本号大于当前版本
        if (remote_version.compare(CURRENT_VERSION) > 0)
        {
            std::cout << "[+] 发现新版本 " << remote_version << "，正在更新..." << std::endl;
            // 初始化CURL用于下载
            CURL *curl = curl_easy_init();
            if (!curl)
            {
                std::cerr << "[-] 初始化CURL失败" << std::endl;
                exit(ERROR_NETWORK);
            }

            // 下载新文件
            std::string new_file = libraryDir + "/update.tmp";

            // 使用优化的下载函数
            if (!DownloadFileWithRetry(curl, download_url, new_file))
            {
                std::cerr << "\n[-] 下载失败，已重试多次" << std::endl;
                curl_easy_cleanup(curl);
                exit(ERROR_DOWNLOAD);
            }

            curl_easy_cleanup(curl);

            // 设置执行权限
            if (chmod(new_file.c_str(), 0755) != 0)
            {
                std::cerr << "[-] 设置文件权限失败" << std::endl;
                remove(new_file.c_str());
                exit(ERROR_FILE_PERMISSION);
            }

            // 替换当前程序
            std::string backup_file = libraryDir + "/backup.so";
            if (rename(libraryPath.c_str(), backup_file.c_str()) != 0)
            {
                std::cerr << "[-] 备份原文件失败: " << strerror(errno) << std::endl;
                remove(new_file.c_str());
                exit(ERROR_FILE_ACCESS);
            }

            if (rename(new_file.c_str(), libraryPath.c_str()) != 0)
            {
                // 如果更新失败，恢复备份
                rename(backup_file.c_str(), libraryPath.c_str());
                std::cerr << "[-] 更新文件失败: " << strerror(errno) << std::endl;
                remove(new_file.c_str());
                exit(ERROR_FILE_ACCESS);
            }

            remove(backup_file.c_str()); // 清理备份文件

            std::cout << "\n[+] 更新完成，请终止后重新运行" << std::endl;
            return true;
        }
        else
        {
            //std::cout << "[+] 当前已是最新版本" << std::endl;
        }

        return false;
    }
};

#endif // CLOUD_DATA_H