#ifndef TXYUN_H
#define TXYUN_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <curl/curl.h>
#include <time.h>
#include <stdarg.h>

#define INITIAL_SIZE 163840  // 初始大小 160KB
#define MAX_SIZE 1048576    // 最大限制 1MB
#define COMMAND_SIZE 8192   // 8KB

// 数据写入结构体
struct WriteData {
    char *buffer;
    size_t size;
    size_t capacity;
};

// 从两个标记之间提取内容
static const char* getSubstring(const char* str, const char* start_tag, const char* end_tag) {
    static char result[INITIAL_SIZE];
    const char* start = strstr(str, start_tag);
    if (start) {
        start += strlen(start_tag);
        const char* end = strstr(start, end_tag);
        if (end) {
            int length = end - start;
            if (length > 0 && length < INITIAL_SIZE) {
                strncpy(result, start, length);
                result[length] = '\0';
                return result;
            }
        }
    }
    return NULL;
}

// 提取title和brief到传入的缓冲区
static bool extractTitleAndBrief(const char* json, char* title, char* brief) {
    const char* title_start = strstr(json, "\"title\":\"");
    const char* brief_start = strstr(json, "\"brief\":\"");
    
    if (title_start && brief_start) {
        title_start += strlen("\"title\":\"");
        brief_start += strlen("\"brief\":\"");
        
        const char* title_end = strchr(title_start, '"');
        const char* brief_end = strchr(brief_start, '"');
        
        if (title_end && brief_end) {
            int title_len = title_end - title_start;
            strncpy(title, title_start, title_len);
            title[title_len] = '\0';
            
            int brief_len = brief_end - brief_start;
            strncpy(brief, brief_start, brief_len);
            brief[brief_len] = '\0';
            
            return true;
        }
    }
    return false;
}

// 初始化WriteData结构
static bool init_write_data(struct WriteData *data) {
    data->buffer = (char*)malloc(INITIAL_SIZE);
    if (!data->buffer) {
        return false;
    }
    data->size = 0;
    data->capacity = INITIAL_SIZE;
    data->buffer[0] = '\0';
    return true;
}

// 释放WriteData结构
static void free_write_data(struct WriteData *data) {
    if (data && data->buffer) {
        free(data->buffer);
        data->buffer = NULL;
        data->size = 0;
        data->capacity = 0;
    }
}

// 扩展缓冲区
static bool expand_buffer(struct WriteData *data, size_t needed_size) {
    if (!data || !data->buffer) return false;
    
    size_t new_size = data->capacity;
    while (new_size < needed_size) {
        new_size *= 2;
        if (new_size > MAX_SIZE) {
            return false;
        }
    }
    
    char *new_buffer = (char*)realloc(data->buffer, new_size);
    if (!new_buffer) {
        return false;
    }
    
    data->buffer = new_buffer;
    data->capacity = new_size;
    return true;
}

// 写入回调函数
static size_t WriteCallback(const char *ptr, size_t size, size_t nmemb, void *userdata)
{
    if (!ptr || !userdata) {
        return 0;
    }
    
    size_t realsize = size * nmemb;
    struct WriteData *data = (struct WriteData*)userdata;
    
    if (!data->buffer) {
        return 0;
    }
    
    if (data->size + realsize + 1 > data->capacity) {
        if (!expand_buffer(data, data->size + realsize + 1)) {
            return 0;
        }
    }
    
    memcpy(data->buffer + data->size, ptr, realsize);
    data->size += realsize;
    data->buffer[data->size] = '\0';
    
    return realsize;
}

// 获取腾讯云分享内容
static bool getTxyunContent(const char* url, char* title, char* brief) {
    if(!url || !title || !brief) {
        return false;
    }
    
    CURL *curl;
    CURLcode res;
    struct WriteData data = {0};
    
    if (!init_write_data(&data)) {
        return false;
    }
    
    res = curl_global_init(CURL_GLOBAL_DEFAULT);
    if(res != CURLE_OK) {
        free_write_data(&data);
        return false;
    }
    
    curl = curl_easy_init();
    if(!curl) {
        curl_global_cleanup();
        free_write_data(&data);
        return false;
    }
    
    size_t (*write_cb)(const char*, size_t, size_t, void*) = WriteCallback;
    
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_cb);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&data);
    curl_easy_setopt(curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);
    curl_easy_setopt(curl, CURLOPT_ACCEPT_ENCODING, "");
    
    res = curl_easy_perform(curl);
    
    bool result = false;
    if(res == CURLE_OK && data.size > 0) {
        const char* start_tag = "<script type=\"text/javascript\">window.syncData = ";
        const char* end_tag = ";</script><script>";
        const char* syncData = getSubstring(data.buffer, start_tag, end_tag);
        
        if (syncData) {
            result = extractTitleAndBrief(syncData, title, brief);
        }
    }
    
    curl_easy_cleanup(curl);
    curl_global_cleanup();
    free_write_data(&data);
    
    return result;
}

#endif // TXYUN_H 