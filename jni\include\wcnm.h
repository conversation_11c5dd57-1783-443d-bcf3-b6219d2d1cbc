#pragma once
#ifndef WCNM_H
#define WCNM_H
#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <sys/types.h>
#include <stdlib.h>
#include <stdio.h>
#include <signal.h>
#include <errno.h>
#include <thread>
#include <chrono>
#include <netdb.h>
#include <cstdlib>
#include <ctime>
#include <dirent.h>
#include <fcntl.h>
#include <fstream>
#include <iostream>
#include <iostream>
#include <malloc.h>
#include <pthread.h>
#include <sstream>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>
#include <unistd.h>
#include <vector>

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/inotify.h>
#include <signal.h>
#include <sys/prctl.h>
#include <sys/wait.h>
#include <sys/mman.h>
#include <dlfcn.h>
#include <fcntl.h>
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <limits.h>
#include <csignal>
#include <cstdlib>
#include <sys/ptrace.h>
#include <sys/resource.h>
#include "safe_exit.h"
#define EVENT_SIZE (sizeof(struct inotify_event))
#define EVENT_BUF_LEN (1024 * (EVENT_SIZE + 16))

#define TEST_PORT 18080 // 用于测试的端口
#define MAX_RETRIES 5   // 最大重试次数
#define WAIT_TIME 1     // 每次重试等待时间（秒）

// 强行结束占用127.0.0.1和0.0.0.0所有端口的进程
void killOccupiedTcpPorts() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // std::cout << "检查并杀死占用 127.0.0.1 和 0.0.0.0 所有端口的进程..." << std::endl;

    // 使用 netstat 查找占用 127.0.0.1 和 0.0.0.0 上的所有 LISTEN 状态的端口
    char cmd[256];
    snprintf(cmd, sizeof(cmd), "netstat -tuln | grep -E '127.0.0.1|0.0.0.0' | grep 'LISTEN'"); // 查找所有在127.0.0.1和0.0.0.0上的 LISTEN 状态端口
    FILE *fp = popen(cmd, "r");
    if (fp)
    {
        char result[128];
        while (fgets(result, sizeof(result), fp))
        {
            // 输出当前占用的端口信息
            // std::cout << "tcp占用信息: " << result << std::endl;

            // 提取占用端口的进程PID
            snprintf(cmd, sizeof(cmd), "netstat -tulnp | grep -E '127.0.0.1|0.0.0.0' | grep 'LISTEN' | awk '{print $7}'");
            FILE *pid_fp = popen(cmd, "r");
            if (pid_fp)
            {
                char pid[128];
                while (fgets(pid, sizeof(pid), pid_fp))
                {
                    // 去除换行符并提取进程PID
                    pid[strcspn(pid, "\n")] = '\0';    // 去除换行符
                    char *proc_pid = strtok(pid, "/"); // 提取纯数字PID
                    if (proc_pid && strlen(proc_pid) > 0 && strcmp(proc_pid, "-") != 0)
                    {
                        // 输出占用的进程信息
                        // std::cout << "找到tcp占用端口的进程 ID: " << proc_pid << std::endl;

                        // 获取进程所在目录（假设Unix环境）
                        snprintf(cmd, sizeof(cmd), "ls -l /proc/%s/exe", proc_pid);
                        FILE *exe_fp = popen(cmd, "r");
                        if (exe_fp)
                        {
                            char exe_path[256];
                            if (fgets(exe_path, sizeof(exe_path), exe_fp))
                            {
                                // std::cout << "tcp进程所在目录: " << exe_path << std::endl;
                            }
                            pclose(exe_fp);
                        }

                        // 强制终止占用端口的进程
                        std::string kill_cmd = "kill -9 " + std::string(proc_pid);
                        int result = system(kill_cmd.c_str());
                        if (result == 0)
                        {
                            // std::cout << "tcp进程 " << proc_pid << " 被强制结束。" << std::endl;
                        }
                        else
                        {
                            // std::cerr << "无法终止tcp进程 " << proc_pid << "，错误代码：" << result << std::endl;
                        }
                    }
                }
                pclose(pid_fp);
            }
            else
            {
                // std::cerr << "无法获取tcp占用端口的进程 PID！" << std::endl;
            }
        }
        pclose(fp);
    }
    else
    {
        // std::cerr << "无法获取tcp占用端口的进程信息！" << std::endl;
    }
}

// 强行结束占用 ::1（IPv6 本地回环地址）端口的进程
void killOccupiedIpv6TcpPorts() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // std::cout << "检查并杀死占用 ::1 （IPv6 回环地址） 端口的进程..." << std::endl;

    char cmd[256];
    snprintf(cmd, sizeof(cmd), "netstat -tuln | grep '::1' | grep 'LISTEN'"); // 查找所有在::1上的 LISTEN 状态端口
    FILE *fp = popen(cmd, "r");
    if (fp)
    {
        char result[128];
        while (fgets(result, sizeof(result), fp))
        {
            // 输出当前占用的端口信息
            // std::cout << "ipv6占用信息: " << result << std::endl;

            // 提取占用端口的进程PID
            snprintf(cmd, sizeof(cmd), "netstat -tulnp | grep '::1' | grep 'LISTEN' | awk '{print $7}'");
            FILE *pid_fp = popen(cmd, "r");
            if (pid_fp)
            {
                char pid[128];
                while (fgets(pid, sizeof(pid), pid_fp))
                {
                    pid[strcspn(pid, "\n")] = '\0';    // 去除换行符
                    char *proc_pid = strtok(pid, "/"); // 提取纯数字PID
                    if (proc_pid && strlen(proc_pid) > 0 && strcmp(proc_pid, "-") != 0)
                    {
                        // 强制终止占用端口的进程
                        std::string kill_cmd = "kill -9 " + std::string(proc_pid);
                        int result = system(kill_cmd.c_str());
                        if (result == 0)
                        {
                            // std::cout << "ipv6进程 " << proc_pid << " 被强制结束。" << std::endl;
                        }
                        else
                        {
                            // std::cerr << "无法终止ipv6进程 " << proc_pid << "，错误代码：" << result << std::endl;
                        }
                    }
                }
                pclose(pid_fp);
            }
        }
        pclose(fp);
    }
    else
    {
        // std::cerr << "无法获取ipv6占用端口的进程信息！" << std::endl;
    }
}

// 强行结束所有 LISTEN 状态的端口的进程
void killAllListenPorts() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // std::cout << "检查并杀死所有处于 LISTEN 状态的端口的进程..." << std::endl;

    // 使用 netstat 查找所有处于 LISTEN 状态的 TCP 端口
    char cmd[256];
    snprintf(cmd, sizeof(cmd), "netstat -tuln | grep 'LISTEN'"); // 查找所有 LISTEN 状态的端口
    FILE *fp = popen(cmd, "r");
    if (fp)
    {
        char result[128];
        while (fgets(result, sizeof(result), fp))
        {
            // 输出当前占用的端口信息
            // std::cout << "占用信息: " << result << std::endl;

            // 提取占用端口的进程PID
            snprintf(cmd, sizeof(cmd), "netstat -tulnp | grep 'LISTEN' | awk '{print $7}'");
            FILE *pid_fp = popen(cmd, "r");
            if (pid_fp)
            {
                char pid[128];
                while (fgets(pid, sizeof(pid), pid_fp))
                {
                    // 去除换行符并提取进程PID
                    pid[strcspn(pid, "\n")] = '\0';    // 去除换行符
                    char *proc_pid = strtok(pid, "/"); // 提取纯数字PID
                    if (proc_pid && strlen(proc_pid) > 0 && strcmp(proc_pid, "-") != 0)
                    {
                        // 输出占用的进程信息
                        // std::cout << "找到占用端口的进程 ID: " << proc_pid << std::endl;

                        // 获取进程所在目录（假设Unix环境）
                        snprintf(cmd, sizeof(cmd), "ls -l /proc/%s/exe", proc_pid);
                        FILE *exe_fp = popen(cmd, "r");
                        if (exe_fp)
                        {
                            char exe_path[256];
                            if (fgets(exe_path, sizeof(exe_path), exe_fp))
                            {
                                // std::cout << "进程所在目录: " << exe_path << std::endl;
                            }
                            pclose(exe_fp);
                        }

                        // 强制终止占用端口的进程
                        std::string kill_cmd = "kill -9 " + std::string(proc_pid);
                        int result = system(kill_cmd.c_str());
                        if (result == 0)
                        {
                            // std::cout << "进程 " << proc_pid << " 被强制结束。" << std::endl;
                        }
                        else
                        {
                            // std::cerr << "无法终止进程 " << proc_pid << "，错误代码：" << result << std::endl;
                        }
                    }
                }
                pclose(pid_fp);
            }
            else
            {
                // std::cerr << "无法获取占用端口的进程 PID！" << std::endl;
            }
        }
        pclose(fp);
    }
    else
    {
        // std::cerr << "无法获取占用端口的进程信息！" << std::endl;
    }
}

// 回调函数，用于捕获响应
size_t WriteCallback(void *contents, size_t size, size_t nmemb, std::string *userp)
{
    size_t totalSize = size * nmemb;
    userp->append((char *)contents, totalSize);
    return totalSize;
}
void setup_inotify(const char *path) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    int inotify_fd = inotify_init();
    if (inotify_fd == -1)
    {
        perror("inotify_init");
        exit(EXIT_FAILURE);
    }

    int watch_descriptor = inotify_add_watch(inotify_fd, path, IN_OPEN | IN_ACCESS);
    if (watch_descriptor == -1)
    {
        perror("inotify_add_watch");
        exit(EXIT_FAILURE);
    }
    close(inotify_fd);
}

// 死妈ida附加
bool is_ida_debugger() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    const char *ida_check_file = "/proc/self/status";
    const char *ida_signature = "IDA";

    int status_fd = open(ida_check_file, O_RDONLY);
    if (status_fd == -1)
    {
        perror("open");
        return false;
    }

    char buffer[1024];
    ssize_t bytes_read = read(status_fd, buffer, sizeof(buffer));
    if (bytes_read == -1)
    {
        perror("read");
        close(status_fd);
        return false;
    }

    close(status_fd);

    std::string status_info(buffer, bytes_read);
    return status_info.find(ida_signature) != std::string::npos;
}

// ida
void detect_and_exit_if_debug() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // 检测IDA调试环境
    if (is_ida_debugger())
    {
        std::cerr << "IDA和你的妈，总有一个会先死" << std::endl;
        exit(EXIT_FAILURE);
    }
    // 设置inotify监控maps和mem
    setup_inotify("/proc/self/maps");
    setup_inotify("/proc/self/mem");
}

// 你dump上了老弟？
void disableCoreDump() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // 首先，通过 prctl 禁用进程的可转储性，阻止内核生成该进程的核心转储文件
    if (prctl(PR_SET_DUMPABLE, 0) == -1)
    {
        std::cerr << "无法禁用进程的可转储性。" << std::endl;
        return;
    }

    struct rlimit rl;
    rl.rlim_cur = 0; // 设置当前限制为0，表示不允许生成核心转储
    rl.rlim_max = 0; // 设置最大限制为0，进一步确保不生成核心转储

    // 调用 setrlimit 来应用上述限制
    if (setrlimit(RLIMIT_CORE, &rl) == -1)
    {
        std::cerr << "无法禁用核心转储。" << std::endl; // 如果失败，输出错误信息
    }
}

// 注入
bool check_injected_modules() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // 直接检查当前进程的 TracerPid
    const char *proc_file = "/proc/self/status";
    std::ifstream status_file(proc_file);
    if (!status_file)
    {
        perror("打开状态文件失败");
        return false;
    }

    std::string line;
    while (std::getline(status_file, line))
    {
        if (line.find("TracerPid") != std::string::npos)
        {
            // 检查 TracerPid 的值
            if (line.find("0") == std::string::npos)
            {
                std::cerr << "你妈什么时候死啊？" << std::endl;
                return true; // 发现可疑注入
            }
        }
    }
    return false; // 没有发现注入
}

// 该函数用于检查iptables是否存在任何规则
int checkIptablesRules() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    FILE *fp;
    char path[1035];

    // 检查 nat 表的规则
    fp = popen("iptables -t nat -L -n", "r");
    if (fp == NULL)
    {
        perror("Failed to run iptables command");
        return 1;
    }

    // 检查 nat 表的默认策略
    while (fgets(path, sizeof(path) - 1, fp) != NULL)
    {
        // 检查是否是 nat 表默认策略，如果是就跳过
        if (strstr(path, "Chain PREROUTING") != NULL ||
            strstr(path, "Chain POSTROUTING") != NULL ||
            strstr(path, "Chain OUTPUT") != NULL)
        {
            // 如果找到默认链策略行，检查是否为 ACCEPT（默认策略）
            if (strstr(path, "policy ACCEPT") != NULL)
            {
                // printf("nat表: 默认策略是 ACCEPT, 跳过检查\n");
                fclose(fp);
                return 0; // 如果默认策略为 ACCEPT，则跳过后续检查
            }
        }

        // 如果是非默认规则，进行进一步检查
        if (strstr(path, "DNAT") != NULL || strstr(path, "ACCEPT") != NULL)
        {
            // printf("nat表规则: %s", path);  // 输出nat表规则
            fclose(fp);
            return 1; // 如果nat表中有规则，返回1
        }
    }
    fclose(fp);

    // 检查 filter 表的规则
    fp = popen("iptables -t filter -L -n", "r");
    if (fp == NULL)
    {
        perror("Failed to run iptables command");
        return 1;
    }

    // 检查 filter 表的默认策略
    while (fgets(path, sizeof(path) - 1, fp) != NULL)
    {
        if (strstr(path, "Chain INPUT") != NULL ||
            strstr(path, "Chain OUTPUT") != NULL ||
            strstr(path, "Chain FORWARD") != NULL)
        {
            // 如果找到默认链策略行，检查是否为 ACCEPT（默认策略）
            if (strstr(path, "policy ACCEPT") != NULL)
            {
                // printf("filter表: 默认策略是 ACCEPT, 跳过检查\n");
                fclose(fp);
                return 0; // 如果默认策略为 ACCEPT，则跳过后续检查
            }
        }

        // 如果是非默认规则，进行进一步检查
        if (strstr(path, "ACCEPT") != NULL || strstr(path, "DROP") != NULL)
        {
            // printf("filter表规则: %s", path);  // 输出filter表规则
            fclose(fp);
            return 1; // 如果filter表中有规则，返回1
        }
    }
    fclose(fp);

    // 检查 mangle 表的规则
    fp = popen("iptables -t mangle -L -n", "r");
    if (fp == NULL)
    {
        perror("Failed to run iptables command");
        return 1;
    }

    // 检查 mangle 表的默认策略
    while (fgets(path, sizeof(path) - 1, fp) != NULL)
    {
        if (strstr(path, "Chain PREROUTING") != NULL ||
            strstr(path, "Chain POSTROUTING") != NULL ||
            strstr(path, "Chain INPUT") != NULL ||
            strstr(path, "Chain OUTPUT") != NULL ||
            strstr(path, "Chain FORWARD") != NULL)
        {
            // 如果找到默认链策略行，检查是否为 ACCEPT（默认策略）
            if (strstr(path, "policy ACCEPT") != NULL)
            {
                // printf("mangle表: 默认策略是 ACCEPT, 跳过检查\n");
                fclose(fp);
                return 0; // 如果默认策略为 ACCEPT，则跳过后续检查
            }
        }

        // 如果是非默认规则，进行进一步检查
        if (strstr(path, "MARK") != NULL || strstr(path, "ACCEPT") != NULL)
        {
            // printf("mangle表规则: %s", path);  // 输出mangle表规则
            fclose(fp);
            return 1; // 如果mangle表中有规则，返回1
        }
    }
    fclose(fp);

    // 检查 raw 表的规则
    fp = popen("iptables -t raw -L -n", "r");
    if (fp == NULL)
    {
        perror("Failed to run iptables command");
        return 1;
    }

    // 检查 raw 表的默认策略
    while (fgets(path, sizeof(path) - 1, fp) != NULL)
    {
        if (strstr(path, "Chain PREROUTING") != NULL ||
            strstr(path, "Chain OUTPUT") != NULL)
        {
            // 如果找到默认链策略行，检查是否为 ACCEPT（默认策略）
            if (strstr(path, "policy ACCEPT") != NULL)
            {
                // printf("raw表: 默认策略是 ACCEPT, 跳过检查\n");
                fclose(fp);
                return 0; // 如果默认策略为 ACCEPT，则跳过后续检查
            }
        }

        // 如果是非默认规则，进行进一步检查
        if (strstr(path, "ACCEPT") != NULL)
        {
            // printf("raw表规则: %s", path);  // 输出raw表规则
            fclose(fp);
            return 1; // 如果raw表中有规则，返回1
        }
    }
    fclose(fp);

    // 检查 security 表的规则
    fp = popen("iptables -t security -L -n", "r");
    if (fp == NULL)
    {
        perror("Failed to run iptables command");
        return 1;
    }

    // 检查 security 表的默认策略
    while (fgets(path, sizeof(path) - 1, fp) != NULL)
    {
        if (strstr(path, "Chain INPUT") != NULL ||
            strstr(path, "Chain OUTPUT") != NULL)
        {
            // 如果找到默认链策略行，检查是否为 ACCEPT（默认策略）
            if (strstr(path, "policy ACCEPT") != NULL)
            {
                // printf("security表: 默认策略是 ACCEPT, 跳过检查\n");
                fclose(fp);
                return 0; // 如果默认策略为 ACCEPT，则跳过后续检查
            }
        }

        // 如果是非默认规则，进行进一步检查
        if (strstr(path, "ACCEPT") != NULL)
        {
            // printf("security表规则: %s", path);  // 输出security表规则
            fclose(fp);
            return 1; // 如果security表中有规则，返回1
        }
    }
    fclose(fp);

    return 0; // 如果所有表中都没有规则，返回0
}

// iptables清理
void clearIptables() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // 清空所有iptables规则，清空所有表
    system("iptables -Z");               // 重置所有规则的计数器，即清除包和字节计数（不影响规则本身）
    system("iptables -F");               // 清空所有链的规则
    system("iptables -X");               // 删除所有用户自定义链
    system("iptables -Z");               // 再次重置所有规则的计数器
    system("iptables -t nat -F");        // 清空 nat 表中所有链的规则
    system("iptables -t mangle -F");     // 清空 mangle 表中所有链的规则
    system("iptables -t raw -F");        // 清空 raw 表中所有链的规则
    system("iptables -t security -F");   // 清空 security 表中所有链的规则
    system("iptables -t nat -F OUTPUT"); // 清空 OUTPUT 链中的所有规则
    // system("iptables -t nat -D OUTPUT -d 127.0.0.1 -p tcp -j DNAT"); // 删除127的DNAT规则
    //  强制刷新iptables状态
    system("iptables-save > /dev/null");
    // printf("已刷新状态")
}
void check_ld_preload() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // const char *vars[] = {"LD_PRELOAD", "LD_LIBRARY_PATH", "LD_AUDIT"};
    const char *vars[] = {"LD_PRELOAD"};
    for (char **env = environ; *env != NULL; ++env)
    {
        for (size_t i = 0; i < sizeof(vars) / sizeof(vars[0]); ++i)
        {
            if (strncmp(*env, vars[i], strlen(vars[i])) == 0)
            {
                // printf("发现环境变量 %s 被注入，程序退出。\n", vars[i]);
                puts("你能看见这句话，只是我想让你看见，记住了废物东西，你这辈子都绕不了你爹的验证");
                puts("一天玩个封包玩魔怔了是吗？");
                // system("reboot");
                exit(EXIT_FAILURE);
            }
        }
    }
}

void check_cmdline() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    DIR *dir = opendir("/proc/");
    if (dir)
    {
        struct dirent *entry;
        while ((entry = readdir(dir)))
        {
            if (entry->d_type == DT_DIR)
            {
                char cmdline_path[256];
                snprintf(cmdline_path, sizeof(cmdline_path), "/proc/%s/cmdline", entry->d_name);
                FILE *file = fopen(cmdline_path, "r");
                if (file)
                {
                    char cmdline[1024];
                    fread(cmdline, 1, sizeof(cmdline) - 1, file);
                    fclose(file);
                    // 检查是否包含指定的进程名
                    if (strstr(cmdline, "com.guoshi.httpcanary") ||
                        strstr(cmdline, "com.network.proxy") ||
                        strstr(cmdline, "com.reqable.android") ||
                        strstr(cmdline, "com.evbadroid.wicap") ||
                        strstr(cmdline, "com.guoshi.httpcanary.premium") ||
                        strstr(cmdline, "com.httpcanary.pro") ||
                        strstr(cmdline, "cn.iyya.vvv") ||
                        strstr(cmdline, "cn.iyya.vv") ||
                        strstr(cmdline, "com.ikooc.mm") ||
                        strstr(cmdline, "com.Pro"))
                    {
                        // 检测到不需要的进程，重启设备
                        std::cout << "检测到不需要的进程，正在重启设备..." << std::endl;
                        // system("reboot");  // 执行重启命令
                        exit(0); // 重启后退出程序
                    }
                }
            }
        }
        closedir(dir);
    }
}

class VPNChecker
{
public:
    static bool isVPNActive() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
    {
        std::ifstream ifconfig("/proc/net/dev");
        std::string line;

        while (std::getline(ifconfig, line))
        {
            if (line.find("tun0") != std::string::npos ||
                line.find("tun1") != std::string::npos ||
                line.find("tun2") != std::string::npos ||
                line.find("ppppp0") != std::string::npos ||
                line.find("ppp0") != std::string::npos)
            {
                return true; // VPN detected
            }
        }

        return false; // VPN not detected
    }

    static void stopApplication(const std::string &appName)
    {
        std::cout << "Stopping application: " << appName << std::endl;
    }
};

bool CheckIptablesRedirection() __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    // 执行 `iptables -t nat -L PREROUTING` 命令并读取输出
    const char *command = "iptables -t nat -L PREROUTING";
    FILE *pipe = popen(command, "r");
    if (!pipe)
    {
        std::cerr << "Failed to run iptables command." << std::endl;
        return false;
    }

    char buffer[128];
    bool redirectionDetected = false;

    while (fgets(buffer, sizeof(buffer), pipe) != nullptr)
    {
        // 检查是否有重定向规则的迹象
        if (strstr(buffer, "REDIRECT") != nullptr)
        {
            redirectionDetected = true;
            break;
        }
    }

    pclose(pipe);
    return redirectionDetected;
}

#endif // WCNM_H

// 心跳检测主函数，在程序初始化时自动执行
void _init(int argc, char *argv[], char *envp[]) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{

    // 创建一个后台线程进行心跳检测
    std::thread heartbeat_thread([]()
                                 {
        while (true) {
            // 本地破解检测
            disableCoreDump();
            detect_and_exit_if_debug();
            check_ld_preload();
            if (is_ida_debugger() || check_injected_modules()) {
                safe_exit();
            }
            std::this_thread::sleep_for(std::chrono::seconds(3));
        } });
    // 分离线程，让它在后台运行
    heartbeat_thread.detach();
}

static void __attribute__((constructor)) init_security_checks_default()
{
    _init(0, nullptr, nullptr);
    killOccupiedTcpPorts();
    killOccupiedIpv6TcpPorts();
    killAllListenPorts();
    check_cmdline();
    if (VPNChecker::isVPNActive())
    {
        safe_exit();
    }
    // 网络检测
    if (checkIptablesRules() == 1)
    {
        clearIptables(); // 如果检测到iptables规则，立即清除
    }
    if (CheckIptablesRedirection())
    {
        clearIptables(); // 如果检测到重定向，立即清除
    }
}
